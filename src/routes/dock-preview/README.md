# SourceFlex Navigation Preview

This is a standalone preview of the navigation system for SourceFlex, showcasing:

## Features Demonstrated

### Desktop Experience
- **MacOS Dock**: Smooth magnification effects, hover animations, active indicators
- **Top Menu Bar**: Traditional navigation with responsive mobile menu
- **User Preference**: Toggle between dock and top menu styles

### Mobile Experience  
- **Bottom-right Menu**: Simple menu button for touch devices
- **Optimized for Tables**: Recognizes that mobile users will primarily view data tables
- **Real Estate Focus**: Maximum screen space for content

### Technical Implementation
- **Svelte 5**: Using latest runes API for reactive state
- **Responsive Design**: Adapts to different screen sizes
- **Performance Optimized**: Smooth 60fps animations
- **Accessibility**: Keyboard navigation and ARIA labels

## Running the Demo

```bash
cd demos/dock-preview
npm install
npm run dev
```

Then open http://localhost:5174

## Preview Features

1. **Navigation Toggle**: Top-right toggle to switch between dock and top menu
2. **Responsive Testing**: Resize browser to see mobile behavior
3. **Interactive Demo**: Click navigation items to see active states
4. **Animation Preview**: Hover over dock items to see magnification

## Mobile Strategy

- **Desktop First**: Primary focus on laptop/desktop experience where tables work well
- **Mobile Simplified**: Bottom-right menu button provides access without cluttering interface
- **Touch Optimized**: Large touch targets and simple interaction patterns

This preview demonstrates the UX strategy without affecting the main codebase.
