<!--
  TopMenuBar.svelte - Traditional top menu bar for desktop navigation
-->
<script>
  import { 
    House, 
    Briefcase, 
    Users, 
    Building2, 
    Target,
    Menu,
    X
  } from 'lucide-svelte';

  let { 
    items = [], 
    onItemClick = () => {},
    activeItems = []
  } = $props();

  let isExpanded = $state(false);

  const iconMap = {
    'dashboard': House,
    'jobs': Briefcase,
    'candidates': Users,
    'clients': Building2,
    'bench': Target
  };

  const toggleExpanded = () => {
    isExpanded = !isExpanded;
  };

  const handleItemClick = (itemId) => {
    onItemClick(itemId);
    isExpanded = false; // Close menu on mobile after click
  };
</script>

<nav class="bg-black/80 backdrop-blur-md border-b border-white/10">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex items-center justify-between h-16">
      <!-- Logo -->
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="flex items-center">
            <div class="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
              SF
            </div>
            <span class="ml-2 text-white font-semibold text-lg">SourceFlex</span>
          </div>
        </div>
      </div>

      <!-- Desktop Navigation -->
      <div class="hidden md:block">
        <div class="ml-10 flex items-baseline space-x-4">
          {#each items as item}
            {@const IconComponent = iconMap[item.id]}
            {@const isActive = activeItems.includes(item.id)}
            
            <button
              onclick={() => handleItemClick(item.id)}
              class="px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center gap-2 {
                isActive 
                  ? 'bg-blue-600 text-white' 
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'
              }"
              aria-current={isActive ? 'page' : undefined}
            >
              {#if IconComponent}
                <IconComponent class="h-4 w-4" />
              {/if}
              {item.name}
            </button>
          {/each}
        </div>
      </div>

      <!-- Mobile menu button -->
      <div class="md:hidden">
        <button
          onclick={toggleExpanded}
          class="bg-gray-800 inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
          aria-expanded={isExpanded}
          aria-label="Toggle navigation menu"
        >
          {#if isExpanded}
            <X class="h-6 w-6" />
          {:else}
            <Menu class="h-6 w-6" />
          {/if}
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile Navigation Menu -->
  {#if isExpanded}
    <div class="md:hidden">
      <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-black/90">
        {#each items as item}
          {@const IconComponent = iconMap[item.id]}
          {@const isActive = activeItems.includes(item.id)}
          
          <button
            onclick={() => handleItemClick(item.id)}
            class="w-full text-left px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 flex items-center gap-3 {
              isActive 
                ? 'bg-blue-600 text-white' 
                : 'text-gray-300 hover:bg-gray-700 hover:text-white'
            }"
            aria-current={isActive ? 'page' : undefined}
          >
            {#if IconComponent}
              <IconComponent class="h-5 w-5" />
            {/if}
            {item.name}
          </button>
        {/each}
      </div>
    </div>
  {/if}
</nav>
