<!--
  SourceFlex Navigation Demo
  Preview of Dock vs Top Menu navigation with mobile-optimized fallback
-->
<script lang="ts">
  import './app.css';
  import MacOSDock from './MacOSDock.svelte';
  import TopMenuBar from './TopMenuBar.svelte';
  import MobileMenu from './MobileMenu.svelte';

  // Navigation preference state
  let navigationMode = $state('dock'); // 'dock' or 'top_menu'
  let currentPage = $state('dashboard');

  // Sample navigation items for SourceFlex
  const navigationItems = [
    {
      id: 'dashboard',
      name: 'Dashboard',
      url: '/dashboard',
      icon: 'https://cdn.jim-nielsen.com/macos/1024/finder-2021-09-10.png?rf=1024'
    },
    {
      id: 'jobs',
      name: 'Jobs',
      url: '/jobs',
      icon: 'https://cdn.jim-nielsen.com/macos/1024/notes-2021-05-25.png?rf=1024'
    },
    {
      id: 'candidates',
      name: 'Candidates',
      url: '/candidates',
      icon: 'https://cdn.jim-nielsen.com/macos/1024/contacts-2021-05-28.png?rf=1024'
    },
    {
      id: 'clients',
      name: 'Clients',
      url: '/clients',
      icon: 'https://cdn.jim-nielsen.com/macos/1024/keynote-2021-05-25.png?rf=1024'
    },
    {
      id: 'bench',
      name: 'Bench Sales',
      url: '/bench',
      icon: 'https://cdn.jim-nielsen.com/macos/1024/calculator-2021-04-29.png?rf=1024'
    }
  ];

  // Handle navigation clicks
  function handleNavigation(itemId: string) {
    currentPage = itemId;
    console.log(`Navigating to: ${itemId}`);
  }

  // Get currently active items
  const activeItems = $derived([currentPage]);

  // Content for each page
  const pageContent: Record<string, { title: string; description: string; content: string }> = {
    dashboard: {
      title: 'Dashboard',
      description: 'Overview of your recruitment metrics and activities',
      content: 'Welcome to SourceFlex! This would show your key metrics, recent activities, and quick actions.'
    },
    jobs: {
      title: 'Jobs Management',
      description: 'Manage job postings and requirements',
      content: 'Here you would see a table of all active job postings, with filters for status, client, and date ranges.'
    },
    candidates: {
      title: 'Candidate Database',
      description: 'Search and manage candidate profiles',
      content: 'This section would display candidate profiles in a searchable table with skills, experience, and status.'
    },
    clients: {
      title: 'Client Management',
      description: 'Manage client relationships and contracts',
      content: 'View and manage all your client accounts, contracts, and communication history.'
    },
    bench: {
      title: 'Bench Sales',
      description: 'Available consultants and bench management',
      content: 'Manage consultants on the bench and match them with suitable opportunities.'
    }
  };

  const currentPageData = $derived(pageContent[currentPage] || pageContent.dashboard);
</script>

<svelte:head>
  <title>SourceFlex Navigation Demo</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
</svelte:head>

<!-- Preference Toggle (Top Right) -->
<div class="preference-toggle">
  <div class="mb-2 text-sm font-medium">Navigation Style</div>
  <div class="toggle-group">
    <button
      class="toggle-btn {navigationMode === 'dock' ? 'active' : ''}"
      onclick={() => navigationMode = 'dock'}
    >
      Dock
    </button>
    <button
      class="toggle-btn {navigationMode === 'top_menu' ? 'active' : ''}"
      onclick={() => navigationMode = 'top_menu'}
    >
      Top Menu
    </button>
  </div>
</div>

<!-- Top Menu Bar (when selected) -->
{#if navigationMode === 'top_menu'}
  <TopMenuBar
    items={navigationItems}
    onItemClick={handleNavigation}
    activeItems={activeItems}
  />
{/if}

<!-- Main Content Area -->
<main class="demo-content" style:margin-top={navigationMode === 'top_menu' ? '64px' : '0'}>
  <div class="demo-card">
    <h1 class="text-3xl font-bold mb-4">{currentPageData.title}</h1>
    <p class="text-lg mb-6 opacity-90">{currentPageData.description}</p>
    <p class="text-sm opacity-75">{currentPageData.content}</p>
    
    <div class="mt-8 p-4 bg-white/10 rounded-lg">
      <h3 class="font-semibold mb-2">Demo Features:</h3>
      <ul class="text-sm space-y-1 text-left">
        <li>✨ <strong>Desktop:</strong> {navigationMode === 'dock' ? 'MacOS-style dock with magnification' : 'Traditional top menu bar'}</li>
        <li>📱 <strong>Mobile:</strong> Simple bottom-right menu button (see bottom right)</li>
        <li>🎯 <strong>Responsive:</strong> Optimized for laptop/desktop use</li>
        <li>⚡ <strong>Performance:</strong> Smooth animations with Cloudflare Workers</li>
        <li>🎨 <strong>User Choice:</strong> Toggle between dock and top menu</li>
      </ul>
    </div>

    <div class="mt-6 text-xs opacity-60">
      Try resizing your browser or switching to mobile view to see responsive behavior
    </div>
  </div>
</main>

<!-- MacOS Dock (when selected and on desktop) -->
{#if navigationMode === 'dock'}
  <div class="dock-container">
    <MacOSDock
      apps={navigationItems}
      onAppClick={handleNavigation}
      openApps={activeItems}
      className="shadow-2xl"
    />
  </div>
{/if}

<!-- Mobile Menu (always present on mobile) -->
<MobileMenu
  items={navigationItems}
  onItemClick={handleNavigation}
  activeItems={activeItems}
/>

<style>
  :global(.dock-container) {
    position: fixed;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 50;
  }

  @media (max-width: 768px) {
    :global(.dock-container) {
      display: none;
    }
  }

  @media (min-width: 769px) {
    :global(.mobile-menu-btn) {
      display: none !important;
    }
  }
</style>
