/* Custom styles for the dock demo */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

.demo-content {
  padding: 2rem;
  color: white;
  text-align: center;
  min-height: 80vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.demo-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 2rem;
  margin: 1rem;
  max-width: 400px;
}

/* Mobile menu styles */
.mobile-menu-btn {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  color: white;
  border: none;
  border-radius: 50%;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.mobile-menu-btn:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.05);
}

.mobile-menu {
  position: fixed;
  bottom: 80px;
  right: 1rem;
  z-index: 999;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0.5rem;
  min-width: 200px;
  transform: translateY(10px) scale(0.95);
  opacity: 0;
  transition: all 0.2s ease;
  pointer-events: none;
}

.mobile-menu.open {
  transform: translateY(0) scale(1);
  opacity: 1;
  pointer-events: all;
}

.mobile-menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  color: white;
  text-decoration: none;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.mobile-menu-item:hover,
.mobile-menu-item.active {
  background: rgba(255, 255, 255, 0.1);
}

/* Hide dock on mobile, show mobile menu */
@media (max-width: 768px) {
  .dock-container {
    display: none;
  }
  
  .mobile-menu-btn {
    display: flex;
  }
}

/* Hide mobile menu on desktop, show dock */
@media (min-width: 769px) {
  .mobile-menu-btn {
    display: none;
  }
  
  .dock-container {
    display: block;
  }
}

/* Preference toggle styles */
.preference-toggle {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.5rem;
  color: white;
}

.toggle-group {
  display: flex;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  padding: 2px;
}

.toggle-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.toggle-btn.active {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}
