<!--
  MobileMenu.svelte - Bottom-right mobile menu for touch devices
  Optimized for table-heavy applications where desktop UX is primary
-->
<script>
  import { 
    House, 
    Briefcase, 
    Users, 
    Building2, 
    Target,
    Menu,
    X
  } from 'lucide-svelte';

  let { 
    items = [], 
    onItemClick = () => {},
    activeItems = []
  } = $props();

  let isOpen = $state(false);

  const iconMap = {
    'dashboard': House,
    'jobs': Briefcase,
    'candidates': Users,
    'clients': Building2,
    'bench': Target
  };

  const toggleMenu = () => {
    isOpen = !isOpen;
  };

  const handleItemClick = (itemId) => {
    onItemClick(itemId);
    isOpen = false; // Close menu after selection
  };

  // Close menu when clicking outside
  const handleOutsideClick = (event) => {
    if (isOpen && !event.target.closest('.mobile-menu') && !event.target.closest('.mobile-menu-btn')) {
      isOpen = false;
    }
  };
</script>

<!-- Mobile Menu Button (Bottom Right) -->
<button
  class="mobile-menu-btn"
  onclick={toggleMenu}
  aria-label="Open navigation menu"
  aria-expanded={isOpen}
>
  {#if isOpen}
    <X class="h-6 w-6" />
  {:else}
    <Menu class="h-6 w-6" />
  {/if}
</button>

<!-- Mobile Menu Popup -->
<div class="mobile-menu {isOpen ? 'open' : ''}">
  <div class="py-1">
    {#each items as item}
      {@const IconComponent = iconMap[item.id]}
      {@const isActive = activeItems.includes(item.id)}
      
      <button
        onclick={() => handleItemClick(item.id)}
        class="mobile-menu-item {isActive ? 'active' : ''}"
        aria-current={isActive ? 'page' : undefined}
      >
        {#if IconComponent}
          <IconComponent class="h-5 w-5" />
        {/if}
        <span class="font-medium">{item.name}</span>
      </button>
    {/each}
  </div>
</div>

<!-- Backdrop for closing menu -->
{#if isOpen}
  <div 
    class="fixed inset-0 z-[998] bg-black/20 backdrop-blur-sm"
    onclick={() => isOpen = false}
    role="button"
    tabindex="-1"
    aria-label="Close menu"
  ></div>
{/if}

<svelte:window onclick={handleOutsideClick} />
