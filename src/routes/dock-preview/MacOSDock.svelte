<!--
  MacOSDock.svelte - Svelte 5 conversion of the React MacOS Dock
  Optimized for SourceFlex with responsive design and mobile considerations
-->
<script lang="ts">
  import { onMount } from 'svelte';

  interface App {
    id: string;
    name: string;
    url: string;
    icon: string;
  }

  // Props
  let {
    apps = [] as App[],
    onAppClick = (id: string) => {},
    openApps = [] as string[],
    className = ''
  } = $props();

  // Reactive state using Svelte 5 runes
  let mouseX = $state<number | null>(null);
  let currentScales = $state<number[]>(apps.map(() => 1));
  let currentPositions = $state<number[]>([]);
  let dockRef = $state<HTMLDivElement | null>(null);
  let iconRefs = $state<HTMLDivElement[]>([]);
  let animationFrameRef = $state<number | undefined>(undefined);
  let lastMouseMoveTime = $state<number>(0);

  // Responsive configuration
  let config = $state({ baseIconSize: 64, maxScale: 1.6, effectWidth: 240 });

  const getResponsiveConfig = () => {
    if (typeof window === 'undefined') {
      return { baseIconSize: 64, maxScale: 1.6, effectWidth: 240 };
    }

    const smallerDimension = Math.min(window.innerWidth, window.innerHeight);
    
    if (smallerDimension < 480) {
      return {
        baseIconSize: Math.max(40, smallerDimension * 0.08),
        maxScale: 1.4,
        effectWidth: smallerDimension * 0.4
      };
    } else if (smallerDimension < 768) {
      return {
        baseIconSize: Math.max(48, smallerDimension * 0.07),
        maxScale: 1.5,
        effectWidth: smallerDimension * 0.35
      };
    } else if (smallerDimension < 1024) {
      return {
        baseIconSize: Math.max(56, smallerDimension * 0.06),
        maxScale: 1.6,
        effectWidth: smallerDimension * 0.3
      };
    } else {
      return {
        baseIconSize: Math.max(64, Math.min(80, smallerDimension * 0.05)),
        maxScale: 1.8,
        effectWidth: 300
      };
    }
  };

  const { baseIconSize, maxScale, effectWidth } = $derived(config);
  const minScale = 1.0;
  const baseSpacing = $derived(Math.max(4, baseIconSize * 0.08));

  // Magnification calculation
  const calculateTargetMagnification = (mousePosition: number | null): number[] => {
    if (mousePosition === null) {
      return apps.map(() => minScale);
    }

    return apps.map((_, index) => {
      const normalIconCenter = (index * (baseIconSize + baseSpacing)) + (baseIconSize / 2);
      const minX = mousePosition - (effectWidth / 2);
      const maxX = mousePosition + (effectWidth / 2);

      if (normalIconCenter < minX || normalIconCenter > maxX) {
        return minScale;
      }

      const theta = ((normalIconCenter - minX) / effectWidth) * 2 * Math.PI;
      const cappedTheta = Math.min(Math.max(theta, 0), 2 * Math.PI);
      const scaleFactor = (1 - Math.cos(cappedTheta)) / 2;

      return minScale + (scaleFactor * (maxScale - minScale));
    });
  };

  // Position calculation
  const calculatePositions = (scales: number[]): number[] => {
    let currentX = 0;

    return scales.map((scale: number) => {
      const scaledWidth = baseIconSize * scale;
      const centerX = currentX + (scaledWidth / 2);
      currentX += scaledWidth + baseSpacing;
      return centerX;
    });
  };

  // Animation loop
  const animateToTarget = () => {
    const targetScales = calculateTargetMagnification(mouseX);
    const targetPositions = calculatePositions(targetScales);
    const lerpFactor = mouseX !== null ? 0.2 : 0.12;

    currentScales = currentScales.map((currentScale, index) => {
      const diff = targetScales[index] - currentScale;
      return currentScale + (diff * lerpFactor);
    });

    currentPositions = currentPositions.map((currentPos, index) => {
      const diff = targetPositions[index] - currentPos;
      return currentPos + (diff * lerpFactor);
    });

    const scalesNeedUpdate = currentScales.some((scale, index) => 
      Math.abs(scale - targetScales[index]) > 0.002
    );
    const positionsNeedUpdate = currentPositions.some((pos, index) => 
      Math.abs(pos - targetPositions[index]) > 0.1
    );
    
    if (scalesNeedUpdate || positionsNeedUpdate || mouseX !== null) {
      animationFrameRef = requestAnimationFrame(animateToTarget);
    }
  };

  // Mouse handlers
  const handleMouseMove = (e: MouseEvent) => {
    const now = performance.now();

    if (now - lastMouseMoveTime < 16) {
      return;
    }

    lastMouseMoveTime = now;

    if (dockRef) {
      const rect = dockRef.getBoundingClientRect();
      const padding = Math.max(8, baseIconSize * 0.12);
      mouseX = e.clientX - rect.left - padding;
    }
  };

  const handleMouseLeave = () => {
    mouseX = null;
  };

  const createBounceAnimation = (element: HTMLElement) => {
    const bounceHeight = Math.max(-8, -baseIconSize * 0.15);
    element.style.transition = 'transform 0.2s ease-out';
    element.style.transform = `translateY(${bounceHeight}px)`;

    setTimeout(() => {
      element.style.transform = 'translateY(0px)';
    }, 200);
  };

  const handleAppClick = (appId: string, index: number) => {
    if (iconRefs[index]) {
      createBounceAnimation(iconRefs[index]);
    }
    onAppClick(appId);
  };

  // Content width calculation
  const contentWidth = $derived(() => {
    if (currentPositions.length === 0) {
      return (apps.length * (baseIconSize + baseSpacing)) - baseSpacing;
    }
    return Math.max(...currentPositions.map((pos, index) => 
      pos + (baseIconSize * currentScales[index]) / 2
    ));
  });

  const padding = $derived(Math.max(8, baseIconSize * 0.12));

  // Initialize
  onMount(() => {
    config = getResponsiveConfig();
    const initialScales = apps.map(() => minScale);
    const initialPositions = calculatePositions(initialScales);
    currentScales = initialScales;
    currentPositions = initialPositions;

    const handleResize = () => {
      config = getResponsiveConfig();
    };

    window.addEventListener('resize', handleResize);
    
    // Start animation
    animationFrameRef = requestAnimationFrame(animateToTarget);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (animationFrameRef) {
        cancelAnimationFrame(animationFrameRef);
      }
    };
  });

  // Initialize icon refs array
  $effect(() => {
    iconRefs = new Array(apps.length).fill(null);
  });

  // Restart animation when dependencies change
  $effect(() => {
    if (animationFrameRef) {
      cancelAnimationFrame(animationFrameRef);
    }
    animationFrameRef = requestAnimationFrame(animateToTarget);
  });
</script>

<div 
  bind:this={dockRef}
  class="backdrop-blur-md {className}"
  style:width="{contentWidth() + padding * 2}px"
  style:background="rgba(45, 45, 45, 0.75)"
  style:border-radius="{Math.max(12, baseIconSize * 0.4)}px"
  style:border="1px solid rgba(255, 255, 255, 0.15)"
  style:box-shadow="
    0 {Math.max(4, baseIconSize * 0.1)}px {Math.max(16, baseIconSize * 0.4)}px rgba(0, 0, 0, 0.4),
    0 {Math.max(2, baseIconSize * 0.05)}px {Math.max(8, baseIconSize * 0.2)}px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2)
  "
  style:padding="{padding}px"
  onmousemove={handleMouseMove}
  onmouseleave={handleMouseLeave}
  role="toolbar"
  aria-label="Navigation dock"
  tabindex="0"
>
  <div 
    class="relative"
    style:height="{baseIconSize}px"
    style:width="100%"
  >
    {#each apps as app, index (app.id)}
      {@const scale = currentScales[index] || 1}
      {@const position = currentPositions[index] || 0}
      {@const scaledSize = baseIconSize * scale}
      
      <div
        bind:this={iconRefs[index]}
        class="absolute cursor-pointer flex flex-col items-center justify-end"
        style:left="{position - scaledSize / 2}px"
        style:bottom="0px"
        style:width="{scaledSize}px"
        style:height="{scaledSize}px"
        style:transform-origin="bottom center"
        style:z-index={Math.round(scale * 10)}
        onclick={() => handleAppClick(app.id, index)}
        onkeydown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleAppClick(app.id, index);
          }
        }}
        tabindex="0"
        role="button"
        aria-label={app.name}
      >
        <img
          src={app.icon}
          alt={app.name}
          width={scaledSize}
          height={scaledSize}
          class="object-contain"
          style:filter="drop-shadow(0 {scale > 1.2 ? Math.max(2, baseIconSize * 0.05) : Math.max(1, baseIconSize * 0.03)}px {scale > 1.2 ? Math.max(4, baseIconSize * 0.1) : Math.max(2, baseIconSize * 0.06)}px rgba(0,0,0,{0.2 + (scale - 1) * 0.15}))"
        />
        
        <!-- App Indicator Dot -->
        {#if openApps.includes(app.id)}
          <div
            class="absolute rounded-full bg-white/80"
            style:bottom="{Math.max(-2, -baseIconSize * 0.05)}px"
            style:left="50%"
            style:transform="translateX(-50%)"
            style:width="{Math.max(3, baseIconSize * 0.06)}px"
            style:height="{Math.max(3, baseIconSize * 0.06)}px"
            style:box-shadow="0 0 4px rgba(0, 0, 0, 0.3)"
          ></div>
        {/if}
      </div>
    {/each}
  </div>
</div>
