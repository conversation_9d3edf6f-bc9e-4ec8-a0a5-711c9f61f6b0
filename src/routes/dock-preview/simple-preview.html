<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SourceFlex Navigation Preview - Simple HTML</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
        }

        .demo-content {
            padding: 2rem;
            text-align: center;
            min-height: 80vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .demo-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem;
            margin: 1rem;
            max-width: 600px;
        }

        /* Mac<PERSON> Dock Styles */
        .dock-container {
            position: fixed;
            bottom: 1rem;
            left: 50%;
            transform: translateX(-50%);
            z-index: 50;
        }

        .dock {
            background: rgba(45, 45, 45, 0.75);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.15);
            padding: 12px;
            display: flex;
            gap: 8px;
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.4),
                0 4px 16px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
        }

        .dock-item {
            width: 64px;
            height: 64px;
            border-radius: 12px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            background: #2d3748;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            color: white;
            text-align: center;
            border: none;
        }

        .dock-item:hover {
            transform: scale(1.3) translateY(-8px);
            z-index: 10;
        }

        .dock-item.active::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 50%;
            transform: translateX(-50%);
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.8);
        }

        /* Top Menu Styles */
        .top-menu {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 50;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 1rem 2rem;
            display: none;
        }

        .top-menu.active {
            display: flex;
        }

        .top-menu-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #3b82f6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .nav-items {
            display: flex;
            gap: 1rem;
        }

        .nav-item {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            background: transparent;
            border: none;
            color: #d1d5db;
            cursor: pointer;
            transition: all 0.2s;
        }

        .nav-item:hover,
        .nav-item.active {
            background: #3b82f6;
            color: white;
        }

        /* Mobile Menu */
        .mobile-menu-btn {
            position: fixed;
            bottom: 1rem;
            right: 1rem;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            color: white;
            border: none;
            border-radius: 50%;
            width: 56px;
            height: 56px;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
            font-size: 20px;
        }

        .mobile-menu-btn:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.05);
        }

        .mobile-menu {
            position: fixed;
            bottom: 80px;
            right: 1rem;
            z-index: 999;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 0.5rem;
            min-width: 200px;
            transform: translateY(10px) scale(0.95);
            opacity: 0;
            transition: all 0.2s ease;
            pointer-events: none;
        }

        .mobile-menu.open {
            transform: translateY(0) scale(1);
            opacity: 1;
            pointer-events: all;
        }

        .mobile-menu-item {
            display: block;
            width: 100%;
            padding: 0.75rem;
            color: white;
            background: transparent;
            border: none;
            border-radius: 8px;
            transition: background-color 0.2s;
            cursor: pointer;
            text-align: left;
        }

        .mobile-menu-item:hover,
        .mobile-menu-item.active {
            background: rgba(255, 255, 255, 0.1);
        }

        /* Preference Toggle */
        .preference-toggle {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 0.5rem;
            color: white;
        }

        .toggle-group {
            display: flex;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            padding: 2px;
            margin-top: 0.5rem;
        }

        .toggle-btn {
            padding: 0.5rem 1rem;
            border: none;
            background: transparent;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 0.875rem;
        }

        .toggle-btn.active {
            background: rgba(255, 255, 255, 0.2);
        }

        /* Responsive Behavior */
        @media (max-width: 768px) {
            .dock-container {
                display: none;
            }
            
            .mobile-menu-btn {
                display: flex;
            }

            .top-menu .nav-items {
                display: none;
            }

            .demo-content {
                margin-top: 0;
            }
        }

        @media (min-width: 769px) {
            .mobile-menu-btn {
                display: none !important;
            }
        }

        .main-with-top-menu {
            margin-top: 64px;
        }
    </style>
</head>
<body>
    <!-- Preference Toggle -->
    <div class="preference-toggle">
        <div style="font-size: 0.875rem; font-weight: 500;">Navigation Style</div>
        <div class="toggle-group">
            <button class="toggle-btn active" onclick="setNavigationMode('dock')" id="dock-btn">
                Dock
            </button>
            <button class="toggle-btn" onclick="setNavigationMode('top_menu')" id="menu-btn">
                Top Menu
            </button>
        </div>
    </div>

    <!-- Top Menu Bar -->
    <div class="top-menu" id="top-menu">
        <div class="top-menu-content">
            <div class="logo">
                <div class="logo-icon">SF</div>
                <span style="font-weight: 600; font-size: 1.125rem;">SourceFlex</span>
            </div>
            <div class="nav-items">
                <button class="nav-item active" onclick="setCurrentPage('dashboard')" data-page="dashboard">Dashboard</button>
                <button class="nav-item" onclick="setCurrentPage('jobs')" data-page="jobs">Jobs</button>
                <button class="nav-item" onclick="setCurrentPage('candidates')" data-page="candidates">Candidates</button>
                <button class="nav-item" onclick="setCurrentPage('clients')" data-page="clients">Clients</button>
                <button class="nav-item" onclick="setCurrentPage('bench')" data-page="bench">Bench Sales</button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="demo-content" id="main-content">
        <div class="demo-card">
            <h1 style="font-size: 2rem; font-weight: bold; margin-bottom: 1rem;" id="page-title">
                SourceFlex Navigation Demo
            </h1>
            <p style="font-size: 1.125rem; margin-bottom: 1.5rem; opacity: 0.9;" id="page-description">
                Experience the future of SaaS navigation with user choice
            </p>
            <p style="font-size: 0.875rem; opacity: 0.75; margin-bottom: 2rem;" id="page-content">
                This demo showcases dock navigation for desktop and simple mobile menu for touch devices.
            </p>
            
            <div style="margin-top: 2rem; padding: 1rem; background: rgba(255, 255, 255, 0.1); border-radius: 8px;">
                <h3 style="font-weight: 600; margin-bottom: 0.5rem;">Demo Features:</h3>
                <ul style="font-size: 0.875rem; text-align: left; line-height: 1.6;">
                    <li>✨ <strong>Desktop:</strong> <span id="desktop-feature">MacOS-style dock with hover magnification</span></li>
                    <li>📱 <strong>Mobile:</strong> Simple bottom-right menu button</li>
                    <li>🎯 <strong>Responsive:</strong> Optimized for laptop/desktop productivity</li>
                    <li>⚡ <strong>Performance:</strong> Smooth animations with Cloudflare Workers</li>
                    <li>🎨 <strong>User Choice:</strong> Toggle between dock and top menu</li>
                </ul>
            </div>

            <div style="margin-top: 1.5rem; font-size: 0.75rem; opacity: 0.6;">
                Try switching navigation modes and resizing your browser
            </div>
        </div>
    </main>

    <!-- MacOS Dock -->
    <div class="dock-container" id="dock-container">
        <div class="dock">
            <button class="dock-item active" onclick="setCurrentPage('dashboard')" title="Dashboard" data-page="dashboard">
                Dashboard
            </button>
            <button class="dock-item" onclick="setCurrentPage('jobs')" title="Jobs" data-page="jobs">
                Jobs
            </button>
            <button class="dock-item" onclick="setCurrentPage('candidates')" title="Candidates" data-page="candidates">
                Candidates
            </button>
            <button class="dock-item" onclick="setCurrentPage('clients')" title="Clients" data-page="clients">
                Clients
            </button>
            <button class="dock-item" onclick="setCurrentPage('bench')" title="Bench Sales" data-page="bench">
                Bench Sales
            </button>
        </div>
    </div>

    <!-- Mobile Menu Button -->
    <button class="mobile-menu-btn" onclick="toggleMobileMenu()" id="mobile-menu-btn">
        ☰
    </button>

    <!-- Mobile Menu -->
    <div class="mobile-menu" id="mobile-menu">
        <button class="mobile-menu-item active" onclick="setCurrentPage('dashboard')" data-page="dashboard">Dashboard</button>
        <button class="mobile-menu-item" onclick="setCurrentPage('jobs')" data-page="jobs">Jobs</button>
        <button class="mobile-menu-item" onclick="setCurrentPage('candidates')" data-page="candidates">Candidates</button>
        <button class="mobile-menu-item" onclick="setCurrentPage('clients')" data-page="clients">Clients</button>
        <button class="mobile-menu-item" onclick="setCurrentPage('bench')" data-page="bench">Bench Sales</button>
    </div>

    <script>
        let currentNavigationMode = 'dock';
        let currentPage = 'dashboard';
        let mobileMenuOpen = false;

        const pageData = {
            dashboard: {
                title: 'Dashboard',
                description: 'Overview of your recruitment metrics and activities',
                content: 'Welcome to SourceFlex! This would show your key metrics, recent activities, and quick actions.'
            },
            jobs: {
                title: 'Jobs Management',
                description: 'Manage job postings and requirements',
                content: 'Here you would see a table of all active job postings, with filters for status, client, and date ranges.'
            },
            candidates: {
                title: 'Candidate Database',
                description: 'Search and manage candidate profiles',
                content: 'This section would display candidate profiles in a searchable table with skills, experience, and status.'
            },
            clients: {
                title: 'Client Management',
                description: 'Manage client relationships and contracts',
                content: 'View and manage all your client accounts, contracts, and communication history.'
            },
            bench: {
                title: 'Bench Sales',
                description: 'Available consultants and bench management',
                content: 'Manage consultants on the bench and match them with suitable opportunities.'
            }
        };

        function setNavigationMode(mode) {
            currentNavigationMode = mode;
            
            // Update toggle buttons
            document.getElementById('dock-btn').classList.toggle('active', mode === 'dock');
            document.getElementById('menu-btn').classList.toggle('active', mode === 'top_menu');
            
            // Show/hide navigation elements
            document.getElementById('dock-container').style.display = mode === 'dock' ? 'block' : 'none';
            document.getElementById('top-menu').classList.toggle('active', mode === 'top_menu');
            
            // Update main content margin
            document.getElementById('main-content').classList.toggle('main-with-top-menu', mode === 'top_menu');
            
            // Update desktop feature text
            document.getElementById('desktop-feature').textContent = 
                mode === 'dock' ? 'MacOS-style dock with hover magnification' : 'Traditional top menu bar';
        }

        function setCurrentPage(page) {
            currentPage = page;
            
            // Update content
            const data = pageData[page];
            document.getElementById('page-title').textContent = data.title;
            document.getElementById('page-description').textContent = data.description;
            document.getElementById('page-content').textContent = data.content;
            
            // Update active states in all navigation elements
            updateActiveStates();
            
            // Close mobile menu if open
            if (mobileMenuOpen) {
                toggleMobileMenu();
            }
        }

        function updateActiveStates() {
            // Update dock items
            document.querySelectorAll('.dock-item').forEach(item => {
                item.classList.toggle('active', item.dataset.page === currentPage);
            });
            
            // Update top menu items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.toggle('active', item.dataset.page === currentPage);
            });
            
            // Update mobile menu items
            document.querySelectorAll('.mobile-menu-item').forEach(item => {
                item.classList.toggle('active', item.dataset.page === currentPage);
            });
        }

        function toggleMobileMenu() {
            mobileMenuOpen = !mobileMenuOpen;
            document.getElementById('mobile-menu').classList.toggle('open', mobileMenuOpen);
            document.getElementById('mobile-menu-btn').textContent = mobileMenuOpen ? '✕' : '☰';
        }

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            if (mobileMenuOpen && 
                !event.target.closest('.mobile-menu') && 
                !event.target.closest('.mobile-menu-btn')) {
                toggleMobileMenu();
            }
        });

        // Initialize
        setNavigationMode('dock');
        setCurrentPage('dashboard');
    </script>
</body>
</html>
