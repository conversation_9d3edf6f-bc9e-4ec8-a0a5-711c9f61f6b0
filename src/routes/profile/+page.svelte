<script lang="ts">
  import ProtectedRoute from '$lib/components/ProtectedRoute.svelte';
  import AppLayout from '$lib/components/layouts/AppLayout.svelte';
  import DeskSwitcher from '$lib/components/DeskSwitcher.svelte';
  import { Button } from '$lib/components/ui/button';
  import { session, userProfile } from '$lib/stores/auth.js';
  import { preferences, navigationStyle, themeMode } from '$lib/stores/preferences.js';
  import toast from 'svelte-5-french-toast';

  let companyName = $state('');
  let timezone = $state('UTC');
  let isLoading = $state(false);
  let isSaving = $state(false);

  // Common timezones
  const timezones = [
    'UTC',
    'America/New_York',
    'America/Chicago', 
    'America/Denver',
    'America/Los_Angeles',
    'Europe/London',
    'Europe/Paris',
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Australia/Sydney'
  ];

  // Initialize form data when profile loads
  $effect(() => {
    if ($userProfile) {
      companyName = $userProfile.company_name || '';
      timezone = $userProfile.timezone || 'UTC';
    }
  });

  async function handleSubmit(event: Event) {
    event.preventDefault();
    isSaving = true;

    try {
      // TODO: Implement GraphQL mutation to update profile
      console.log('Updating profile:', {
        company_name: companyName,
        timezone: timezone,
        onboarding_completed: true
      });

      // Update local store
      userProfile.update(profile => profile ? {
        ...profile,
        company_name: companyName,
        timezone: timezone,
        onboarding_completed: true
      } : null);

      toast.success('Profile updated successfully!');
      
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    } finally {
      isSaving = false;
    }
  }

  async function handleDeskChange(newDesk: 'recruitment' | 'bench_sales') {
    try {
      // TODO: Implement GraphQL mutation
      console.log('Updating desk to:', newDesk);
      
      userProfile.update(profile => profile ? {...profile, current_desk: newDesk} : null);
      toast.success(`Switched to ${newDesk === 'recruitment' ? 'Recruitment' : 'Bench Sales'} desk`);
    } catch (error) {
      console.error('Error updating desk:', error);
      toast.error('Failed to switch desk');
    }
  }
</script>

<svelte:head>
  <title>Profile - SourceFlex</title>
</svelte:head>

<ProtectedRoute>
  {#snippet children()}
    <AppLayout>
      {#snippet children()}
        <div class="p-6">
          <!-- Page Header -->
          <div class="mb-6">
            <h1 class="text-2xl font-bold text-foreground">Profile Settings</h1>
            <p class="text-muted-foreground">Manage your account settings and preferences</p>
          </div>

          <div class="max-w-2xl space-y-6">
            <!-- Personal Information Card -->
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
              <h2 class="text-lg font-medium mb-6">Personal Information</h2>

              <form onsubmit={handleSubmit} class="space-y-6">
                <!-- Email (read-only) -->
                <div class="space-y-2">
                  <label for="email" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Email Address
                  </label>
                  <input
                    id="email"
                    type="email"
                    value={$session?.user?.email || ''}
                    readonly
                    class="flex h-10 w-full rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground cursor-not-allowed"
                  />
                  <p class="text-xs text-muted-foreground">
                    Email cannot be changed. Contact support if needed.
                  </p>
                </div>

                <!-- Company Name -->
                <div class="space-y-2">
                  <label for="company" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Company Name
                  </label>
                  <input
                    id="company"
                    type="text"
                    bind:value={companyName}
                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    placeholder="Enter your company name"
                  />
                </div>

                <!-- Timezone -->
                <div class="space-y-2">
                  <label for="timezone" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Timezone
                  </label>
                  <select
                    id="timezone"
                    bind:value={timezone}
                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    {#each timezones as tz}
                      <option value={tz}>{tz}</option>
                    {/each}
                  </select>
                </div>

                <!-- Current Desk -->
                <div class="space-y-2">
                  <label for="desk-switcher" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Current Desk
                  </label>
                  <div id="desk-switcher" class="pt-2">
                    <DeskSwitcher onDeskChange={handleDeskChange} />
                  </div>
                </div>

                <!-- Email Domain (read-only) -->
                <div class="space-y-2">
                  <label for="email-domain" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Email Domain
                  </label>
                  <input
                    id="email-domain"
                    type="text"
                    value={$userProfile?.email_domain || 'N/A'}
                    readonly
                    class="flex h-10 w-full rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground cursor-not-allowed"
                  />
                  <p class="text-xs text-muted-foreground">
                    Automatically extracted from your email address.
                  </p>
                </div>

                <!-- Save Button -->
                <div class="flex justify-end">
                  <Button type="submit" disabled={isSaving}>
                    {#if isSaving}
                      <svg class="animate-spin -ml-1 mr-3 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Saving...
                    {:else}
                      Save Changes
                    {/if}
                  </Button>
                </div>
              </form>
            </div>

            <!-- UI Preferences Card -->
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
              <h2 class="text-lg font-medium mb-6">Interface Preferences</h2>

              <div class="space-y-6">
                <!-- Navigation Style -->
                <div class="space-y-3">
                  <label class="text-sm font-medium leading-none">
                    Navigation Style
                  </label>
                  <p class="text-xs text-muted-foreground mb-3">
                    Choose how you'd like to navigate SourceFlex
                  </p>
                  
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <!-- Dock Option -->
                    <button
                      type="button"
                      onclick={() => preferences.setNavigation({ style: 'dock' })}
                      class="relative rounded-lg border-2 p-4 text-left transition-all hover:bg-accent/50 {$navigationStyle === 'dock' ? 'border-primary bg-primary/5' : 'border-border'}"
                    >
                      <div class="flex items-center gap-3">
                        <div class="rounded-lg bg-primary/10 p-2">
                          <svg class="h-5 w-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <rect x="3" y="18" width="18" height="3" rx="1"/>
                            <circle cx="7" cy="19.5" r="0.5"/>
                            <circle cx="12" cy="19.5" r="0.5"/>
                            <circle cx="17" cy="19.5" r="0.5"/>
                          </svg>
                        </div>
                        <div>
                          <h3 class="font-medium">MacOS Dock</h3>
                          <p class="text-sm text-muted-foreground">Modern dock at bottom</p>
                        </div>
                      </div>
                      {#if $navigationStyle === 'dock'}
                        <div class="absolute right-2 top-2">
                          <svg class="h-4 w-4 text-primary" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                          </svg>
                        </div>
                      {/if}
                    </button>

                    <!-- Top Menu Option -->
                    <button
                      type="button"
                      onclick={() => preferences.setNavigation({ style: 'top_menu' })}
                      class="relative rounded-lg border-2 p-4 text-left transition-all hover:bg-accent/50 {$navigationStyle === 'top_menu' ? 'border-primary bg-primary/5' : 'border-border'}"
                    >
                      <div class="flex items-center gap-3">
                        <div class="rounded-lg bg-primary/10 p-2">
                          <svg class="h-5 w-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <rect x="3" y="3" width="18" height="3" rx="1"/>
                            <line x1="7" y1="4.5" x2="7" y2="4.5"/>
                            <line x1="12" y1="4.5" x2="12" y2="4.5"/>
                            <line x1="17" y1="4.5" x2="17" y2="4.5"/>
                          </svg>
                        </div>
                        <div>
                          <h3 class="font-medium">Top Menu</h3>
                          <p class="text-sm text-muted-foreground">Traditional menu bar</p>
                        </div>
                      </div>
                      {#if $navigationStyle === 'top_menu'}
                        <div class="absolute right-2 top-2">
                          <svg class="h-4 w-4 text-primary" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                          </svg>
                        </div>
                      {/if}
                    </button>
                  </div>
                </div>

                <!-- Theme Mode -->
                <div class="space-y-3">
                  <label class="text-sm font-medium leading-none">
                    Theme
                  </label>
                  <p class="text-xs text-muted-foreground mb-3">
                    Choose your preferred color scheme
                  </p>
                  
                  <div class="grid grid-cols-3 gap-3">
                    {#each ['light', 'dark', 'system'] as theme}
                      <button
                        type="button"
                        onclick={() => preferences.setTheme({ mode: theme as any })}
                        class="rounded-lg border-2 p-3 text-center transition-all hover:bg-accent/50 {$themeMode === theme ? 'border-primary bg-primary/5' : 'border-border'}"
                      >
                        <div class="mx-auto mb-2 h-6 w-6">
                          {#if theme === 'light'}
                            <svg class="h-6 w-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <circle cx="12" cy="12" r="5"/>
                              <line x1="12" y1="1" x2="12" y2="3"/>
                              <line x1="12" y1="21" x2="12" y2="23"/>
                              <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
                              <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
                              <line x1="1" y1="12" x2="3" y2="12"/>
                              <line x1="21" y1="12" x2="23" y2="12"/>
                              <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
                              <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
                            </svg>
                          {:else if theme === 'dark'}
                            <svg class="h-6 w-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                            </svg>
                          {:else}
                            <svg class="h-6 w-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                              <line x1="8" y1="21" x2="16" y2="21"/>
                              <line x1="12" y1="17" x2="12" y2="21"/>
                            </svg>
                          {/if}
                        </div>
                        <span class="text-sm font-medium capitalize">{theme}</span>
                      </button>
                    {/each}
                  </div>
                </div>
              </div>
            </div>

            <!-- Profile Status Card -->
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
              <h3 class="text-lg font-medium mb-4">Profile Status</h3>
              
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-muted-foreground">Account Status</span>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Active
                  </span>
                </div>
                
                <div class="flex items-center justify-between">
                  <span class="text-sm text-muted-foreground">Email Verified</span>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Verified
                  </span>
                </div>
                
                <div class="flex items-center justify-between">
                  <span class="text-sm text-muted-foreground">Onboarding</span>
                  {#if $userProfile?.onboarding_completed}
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Completed
                    </span>
                  {:else}
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      Pending
                    </span>
                  {/if}
                </div>
                
                <div class="flex items-center justify-between">
                  <span class="text-sm text-muted-foreground">User Role</span>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {$session?.user?.defaultRole || 'user'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      {/snippet}
    </AppLayout>
  {/snippet}
</ProtectedRoute>
