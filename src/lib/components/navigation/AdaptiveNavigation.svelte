<!--
  AdaptiveNavigation.svelte
  Smart navigation component that switches between dock and top menu based on user preference
-->
<script lang="ts">
  import { navigationStyle, isDockNavigation } from '$lib/stores/preferences.js';
  import AppSidebar from '$lib/components/ui/AppSidebar.svelte';
  import MacOSDock from '$lib/components/ui/MacOSDock.svelte';
  import TopMenuBar from '$lib/components/ui/TopMenuBar.svelte';
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  
  // Navigation items shared between both navigation types
  const navigationItems = [
    {
      id: 'dashboard',
      name: 'Dashboard',
      url: '/dashboard',
      icon: '/icons/dashboard.png', // Update with actual icons
      isActive: () => $page.url.pathname === '/dashboard'
    },
    {
      id: 'jobs',
      name: 'Jobs',
      url: '/jobs',
      icon: '/icons/jobs.png',
      isActive: () => $page.url.pathname.startsWith('/jobs')
    },
    {
      id: 'candidates',
      name: 'Candidates',
      url: '/candidates',
      icon: '/icons/candidates.png',
      isActive: () => $page.url.pathname.startsWith('/candidates')
    },
    {
      id: 'clients',
      name: 'Clients',
      url: '/clients',
      icon: '/icons/clients.png',
      isActive: () => $page.url.pathname.startsWith('/clients')
    },
    {
      id: 'bench',
      name: 'Bench Sales',
      url: '/bench',
      icon: '/icons/bench.png',
      isActive: () => $page.url.pathname.startsWith('/bench')
    }
  ];

  // Handle navigation clicks
  function handleNavigation(itemId: string) {
    const item = navigationItems.find(nav => nav.id === itemId);
    if (item) {
      goto(item.url);
    }
  }

  // Get currently active items for dock indicators
  $: activeItems = navigationItems.filter(item => item.isActive()).map(item => item.id);
</script>

{#if $isDockNavigation}
  <!-- MacOS Dock Navigation -->
  <div class="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50">
    <MacOSDock
      apps={navigationItems}
      onAppClick={handleNavigation}
      openApps={activeItems}
      className="backdrop-blur-md shadow-2xl"
    />
  </div>
{:else}
  <!-- Traditional Sidebar Navigation -->
  <AppSidebar />
{/if}

<!-- Top Menu Bar (only when top_menu preference is selected) -->
{#if $navigationStyle === 'top_menu'}
  <div class="fixed top-0 left-0 right-0 z-50">
    <TopMenuBar
      items={navigationItems}
      onItemClick={handleNavigation}
      activeItems={activeItems}
    />
  </div>
{/if}
