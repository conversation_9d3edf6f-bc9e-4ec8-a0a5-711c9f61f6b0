import { writable, derived } from 'svelte/store';
import { browser } from '$app/environment';
import { userProfile } from './auth.js';

// Navigation preferences type
export interface NavigationPreferences {
  style: 'dock' | 'top_menu';
  autoHide: boolean;
  iconSize: 'small' | 'medium' | 'large';
  position: 'bottom' | 'top' | 'left' | 'right'; // Future: allow different positions
}

// Theme preferences type
export interface ThemePreferences {
  mode: 'light' | 'dark' | 'system';
  accentColor: string;
  compactMode: boolean;
}

// All user preferences
export interface UserPreferences {
  navigation: NavigationPreferences;
  theme: ThemePreferences;
  timezone: string;
  dateFormat: string;
  timeFormat: '12h' | '24h';
}

// Default preferences
const defaultPreferences: UserPreferences = {
  navigation: {
    style: 'dock', // Default to dock for modern UX
    autoHide: false,
    iconSize: 'medium',
    position: 'bottom'
  },
  theme: {
    mode: 'system',
    accentColor: '#3b82f6', // Blue
    compactMode: false
  },
  timezone: 'UTC',
  dateFormat: 'MM/DD/YYYY',
  timeFormat: '12h'
};

// Local storage key
const PREFERENCES_KEY = 'sourceflex_preferences';

// Create the preferences store
function createPreferencesStore() {
  const { subscribe, set, update } = writable<UserPreferences>(defaultPreferences);

  return {
    subscribe,
    set,
    update,
    
    // Initialize preferences from local storage and user profile
    init: () => {
      if (!browser) return;
      
      try {
        const stored = localStorage.getItem(PREFERENCES_KEY);
        if (stored) {
          const parsed = JSON.parse(stored);
          update(prefs => ({ ...prefs, ...parsed }));
        }
      } catch (error) {
        console.warn('Failed to load preferences from localStorage:', error);
      }
    },
    
    // Save preferences to local storage
    save: (prefs: UserPreferences) => {
      if (!browser) return;
      
      try {
        localStorage.setItem(PREFERENCES_KEY, JSON.stringify(prefs));
        set(prefs);
      } catch (error) {
        console.warn('Failed to save preferences to localStorage:', error);
      }
    },
    
    // Update navigation preference
    setNavigation: (navigation: Partial<NavigationPreferences>) => {
      update(prefs => {
        const newPrefs = {
          ...prefs,
          navigation: { ...prefs.navigation, ...navigation }
        };
        
        if (browser) {
          try {
            localStorage.setItem(PREFERENCES_KEY, JSON.stringify(newPrefs));
          } catch (error) {
            console.warn('Failed to save navigation preferences:', error);
          }
        }
        
        return newPrefs;
      });
    },
    
    // Update theme preference
    setTheme: (theme: Partial<ThemePreferences>) => {
      update(prefs => {
        const newPrefs = {
          ...prefs,
          theme: { ...prefs.theme, ...theme }
        };
        
        if (browser) {
          try {
            localStorage.setItem(PREFERENCES_KEY, JSON.stringify(newPrefs));
          } catch (error) {
            console.warn('Failed to save theme preferences:', error);
          }
        }
        
        return newPrefs;
      });
    },
    
    // Reset to defaults
    reset: () => {
      if (browser) {
        localStorage.removeItem(PREFERENCES_KEY);
      }
      set(defaultPreferences);
    }
  };
}

// Export the preferences store
export const preferences = createPreferencesStore();

// Derived stores for specific preferences
export const navigationStyle = derived(
  preferences,
  ($prefs) => $prefs.navigation.style
);

export const themeMode = derived(
  preferences,
  ($prefs) => $prefs.theme.mode
);

export const isDockNavigation = derived(
  navigationStyle,
  ($style) => $style === 'dock'
);

export const isTopMenuNavigation = derived(
  navigationStyle,
  ($style) => $style === 'top_menu'
);

// Initialize preferences when module loads
if (browser) {
  preferences.init();
}
